import { cors } from "hono/cors";
import { notFound, serveEmojiFavicon } from "stoker/middlewares";

import { configureOpenAPI } from "@/lib/openapi";
import { TelegramBotService } from "@/lib/telegram/bot-service";
import { authMiddleware } from "@/middlewares/auth.middleware";
import { errorHandler } from "@/middlewares/error-handler.middleware";
import { pinoLogger } from "@/middlewares/pino-logger.middleware";
import { securityHeaders } from "@/middlewares/security.middleware";
import authors from "@/routes/authors";
import telegramTest from "@/routes/test/telegram";
import env from "./env";
import { configureAuth } from "./lib/auth";
import { createRouter } from "./lib/helpers";
import index from "./routes";
import candidates from "./routes/candidates";
import jobApplications from "./routes/job-applications";
import jobVacancies from "./routes/job-vacancies";
import users from "./routes/users";

export function createApp() {
  const app = createRouter();

  if (!env.isTest) {
    TelegramBotService.startScheduledTasks();
  }

  const trustedOrigins: string[] = ["https://crm-group-working.com"];
  if (env.isDevelopment) {
    trustedOrigins.push("http://localhost:3000");
    trustedOrigins.push("http://localhost:9999");
    trustedOrigins.push("http://localhost:5173");
  }
  trustedOrigins.push("http://localhost:5173");

  app.use(
    "*",
    cors({
      origin: trustedOrigins,
      allowHeaders: ["Content-Type", "Authorization"],
      allowMethods: ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
      exposeHeaders: ["Content-Length"],
      maxAge: 600,
      credentials: true,
    }),
  );
  app.use(serveEmojiFavicon("📄"));
  app.use(pinoLogger());
  app.use(securityHeaders);
  app.use("*", authMiddleware);
  app.notFound(notFound);
  app.onError(errorHandler);

  return app;
}

const app = createApp()
  .route("/", index)
  .route("/job-vacancies", jobVacancies)
  .route("/applications", jobApplications)
  .route("/candidates", candidates)
  .route("/users", users)
  .route("/authors", authors);

if (env.isDevelopment) {
  app.route("/test/telegram", telegramTest);
}

configureAuth(app);
configureOpenAPI(app);

export type AppType = typeof app;
// export const appClient = testClient(app);
export default app;
