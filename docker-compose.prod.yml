version: "3.8"

services:
  api:
    build:
      context: .
      dockerfile: Dockerfile
    image: gw-crm-api:prod
    ports:
      - "9090:9090"
    secrets:
      - gw_crm_database_url
      - gw_crm_database_auth_token
      - gw_crm_better_auth_secret
      - gw_crm_resend_api_key
    environment:
      - NODE_ENV=production
      - PORT=9090
      - LOG_LEVEL=info
      - BASE_URL=https://api.crm-group-working.com
      - HOST=0.0.0.0
      - EMAIL_FROM=<EMAIL>
      - RESEND_API_KEY_FILE=/run/secrets/gw_crm_resend_api_key
      - DATABASE_URL_FILE=/run/secrets/gw_crm_database_url
      - DATABASE_AUTH_TOKEN_FILE=/run/secrets/gw_crm_database_auth_token
      - BETTER_AUTH_SECRET_FILE=/run/secrets/gw_crm_better_auth_secret
      - DOCS_BASIC_AUTH_USERNAME=admin
      - DOCS_BASIC_AUTH_PASSWORD=8oa3T^T6i*F6K$9pNqL@t9Hb
    healthcheck:
      test: [CMD, wget, --spider, -q, "http://0.0.0.0:9090/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    deploy:
      replicas: 2
      update_config:
        parallelism: 1
        delay: 10s
        order: start-first
      restart_policy:
        condition: on-failure
        max_attempts: 3
        window: 120s
    networks:
      - api-network

networks:
  api-network:
    driver: overlay

secrets:
  gw_crm_database_url:
    external: true
  gw_crm_database_auth_token:
    external: true
  gw_crm_better_auth_secret:
    external: true
  gw_crm_resend_api_key:
    external: true
